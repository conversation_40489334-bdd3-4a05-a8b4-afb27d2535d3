{"name": "chainops-backend", "version": "1.0.0", "description": "ChainOps Backend Service", "main": "dist/index.js", "scripts": {"build": "tsc --noEmitOnError false", "build:strict": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@kubernetes/client-node": "^1.3.0", "@prisma/client": "^6.9.0", "@types/multer": "^1.4.13", "@types/node-cron": "^3.0.11", "@types/request-ip": "^0.0.41", "aws-sdk": "^2.1692.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "compression": "^1.8.0", "cors": "^2.8.5", "dockerode": "^4.0.7", "dotenv": "^16.4.5", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "limiter": "^3.0.0", "minio": "^7.1.3", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cron": "^4.1.0", "nodemailer": "^7.0.3", "pg": "^8.11.3", "redis": "^4.6.13", "request-ip": "^3.3.0", "socket.io": "^4.8.1"}, "devDependencies": {"@types/aws-sdk": "^0.0.42", "@types/axios": "^0.9.36", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/bull": "^3.15.9", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/dockerode": "^3.3.40", "@types/express": "^5.0.3", "@types/express-validator": "^2.20.33", "@types/helmet": "^0.0.48", "@types/ioredis": "^4.28.10", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.10", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.2", "@types/twilio": "^3.19.2", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "@types/ws": "^8.18.1", "jest": "^29.7.0", "nodemon": "^3.1.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "typescript": "^5.8.3"}}