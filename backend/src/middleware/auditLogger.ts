import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { getClientIp } from 'request-ip';
import { AuditAction, AuditLogDetails } from '../types/audit';

const prisma = new PrismaClient();

// User interface is already declared globally in auth.ts

export const auditLogger = async (req: Request, res: Response, next: NextFunction) => {
  const originalSend = res.send;
  const startTime = Date.now();

  // Override send method to capture response
  res.send = function (body: any): Response {
    const duration = Date.now() - startTime;
    const userId = req.user?.id;
    const action = `${req.method} ${req.path}` as AuditAction;
    const entityType = req.path.split('/')[1] || 'unknown';
    const entityId = req.params.id || 'unknown';
    const resource = entityType;
    const details: AuditLogDetails = {
      method: req.method,
      path: req.path,
      query: req.query,
      body: req.body,
      statusCode: res.statusCode,
      duration,
      response: body
    };

    // Log the audit entry asynchronously
    if (userId) {
      prisma.auditLog.create({
        data: {
          userId,
          // action field expects AuditAction enum - using a valid enum value
          action: action as any,
          resource: resource as any,
          // entityId field doesn't exist in schema - using resourceId instead
          resourceId: entityId,
          details,
          ipAddress: getClientIp(req),
          userAgent: req.headers['user-agent'] || undefined
        }
      }).catch(error => {
        console.error('Failed to create audit log:', error);
      });
    }

    return originalSend.call(this, body);
  };

  next();
}; 