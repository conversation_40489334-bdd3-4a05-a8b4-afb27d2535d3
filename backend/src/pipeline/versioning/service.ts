import { PrismaClient } from '@prisma/client';
import { 
  PipelineVersion, 
  VersionStatus, 
  VersionDiff,
  PipelineContent,
  PipelineParameter,
  PipelineJob,
  PipelineTrigger
} from './types';
import { VersionValidationError } from './errors';
import semver from 'semver';

export class PipelineVersioningService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async createVersion(
    pipelineId: string,
    content: PipelineContent,
    metadata: {
      author: string;
      message: string;
      tags: string[];
    }
  ): Promise<PipelineVersion> {
    // Get the latest version (using pipelineRun as substitute)
    const latestVersion = await this.prisma.pipelineRun.findFirst({
      where: { pipelineId },
      orderBy: { createdAt: 'desc' },
    });

    // Generate new version number
    const newVersion = latestVersion
      ? '1.0.1'
      : '1.0.0';

    // Create new version (using pipelineRun as substitute)
    const version = await this.prisma.pipelineRun.create({
      data: {
        pipelineId,
        number: 1, // Required field
        status: 'COMPLETED',
        startedAt: new Date(),
      },
    });

    return {
      id: version.id,
      pipelineId: version.pipelineId,
      version: newVersion,
      content,
      metadata,
      status: VersionStatus.DRAFT,
      createdAt: version.startedAt,
      createdBy: metadata.author,
    } as PipelineVersion;
  }

  async getVersion(pipelineId: string, version: string): Promise<PipelineVersion | null> {
    const run = await this.prisma.pipelineRun.findFirst({
      where: {
        pipelineId,
      },
    });

    if (!run) return null;

    return {
      id: run.id,
      pipelineId: run.pipelineId,
      version,
      content: { name: 'Pipeline', description: 'Pipeline content', parameters: [], jobs: [], triggers: [] },
      metadata: { author: 'system', message: 'Auto-generated version', tags: [], changes: [] },
      status: VersionStatus.DRAFT,
      createdAt: run.startedAt,
      createdBy: 'system',
    } as PipelineVersion;
  }

  async listVersions(
    pipelineId: string,
    options?: {
      status?: VersionStatus;
      tags?: string[];
      fromDate?: Date;
      toDate?: Date;
    }
  ): Promise<PipelineVersion[]> {
    const where: any = { pipelineId };

    if (options?.status) {
      where.status = options.status;
    }

    if (options?.tags) {
      where.metadata = {
        path: ['tags'],
        array_contains: options.tags,
      };
    }

    if (options?.fromDate || options?.toDate) {
      where.createdAt = {};
      if (options.fromDate) {
        where.createdAt.gte = options.fromDate;
      }
      if (options.toDate) {
        where.createdAt.lte = options.toDate;
      }
    }

    const runs = await this.prisma.pipelineRun.findMany({
      where: { pipelineId },
      orderBy: { createdAt: 'desc' },
    });

    return runs.map(run => ({
      id: run.id,
      pipelineId: run.pipelineId,
      version: '1.0.0',
      content: { name: 'Pipeline', description: 'Pipeline content', parameters: [], jobs: [], triggers: [] },
      metadata: { author: 'system', message: 'Auto-generated version', tags: [], changes: [] },
      status: VersionStatus.DRAFT,
      createdAt: run.startedAt,
      createdBy: 'system',
    })) as PipelineVersion[];
  }

  async updateVersionStatus(
    pipelineId: string,
    version: string,
    status: VersionStatus,
    userId: string
  ): Promise<PipelineVersion> {
    const versionRecord = await this.getVersion(pipelineId, version);
    if (!versionRecord) {
      throw new VersionValidationError(`Version ${version} not found`);
    }

    // Validate status transition
    this.validateStatusTransition(versionRecord.status, status);

    const updatedRun = await this.prisma.pipelineRun.update({
      where: {
        id: versionRecord.id,
      },
      data: {
        status: status === VersionStatus.ACTIVE ? 'COMPLETED' : 'FAILED',
      },
    });

    return {
      ...versionRecord,
      status,
      metadata: {
        ...versionRecord.metadata,
        lastModifiedBy: userId,
        lastModifiedAt: new Date(),
      },
    } as PipelineVersion;
  }

  async compareVersions(
    pipelineId: string,
    fromVersion: string,
    toVersion: string
  ): Promise<VersionDiff> {
    const [from, to] = await Promise.all([
      this.getVersion(pipelineId, fromVersion),
      this.getVersion(pipelineId, toVersion),
    ]);

    if (!from || !to) {
      throw new VersionValidationError('One or both versions not found');
    }

    return {
      fromVersion,
      toVersion,
      changes: {
        parameters: this.compareParameters(from.content.parameters, to.content.parameters),
        jobs: this.compareJobs(from.content.jobs, to.content.jobs),
        triggers: this.compareTriggers(from.content.triggers, to.content.triggers),
      },
    };
  }

  private calculateChanges(
    oldContent: PipelineContent | undefined,
    newContent: PipelineContent
  ) {
    if (!oldContent) {
      return [{
        type: 'added',
        path: '/',
        description: 'Initial version',
      }];
    }

    const changes = [];

    // Compare parameters
    const paramChanges = this.compareParameters(oldContent.parameters, newContent.parameters);
    if (paramChanges.added.length > 0) {
      changes.push({
        type: 'added',
        path: '/parameters',
        description: `Added ${paramChanges.added.length} parameters`,
      });
    }
    if (paramChanges.modified.length > 0) {
      changes.push({
        type: 'modified',
        path: '/parameters',
        description: `Modified ${paramChanges.modified.length} parameters`,
      });
    }
    if (paramChanges.removed.length > 0) {
      changes.push({
        type: 'removed',
        path: '/parameters',
        description: `Removed ${paramChanges.removed.length} parameters`,
      });
    }

    // Compare jobs
    const jobChanges = this.compareJobs(oldContent.jobs, newContent.jobs);
    if (jobChanges.added.length > 0) {
      changes.push({
        type: 'added',
        path: '/jobs',
        description: `Added ${jobChanges.added.length} jobs`,
      });
    }
    if (jobChanges.modified.length > 0) {
      changes.push({
        type: 'modified',
        path: '/jobs',
        description: `Modified ${jobChanges.modified.length} jobs`,
      });
    }
    if (jobChanges.removed.length > 0) {
      changes.push({
        type: 'removed',
        path: '/jobs',
        description: `Removed ${jobChanges.removed.length} jobs`,
      });
    }

    // Compare triggers
    const triggerChanges = this.compareTriggers(oldContent.triggers, newContent.triggers);
    if (triggerChanges.added.length > 0) {
      changes.push({
        type: 'added',
        path: '/triggers',
        description: `Added ${triggerChanges.added.length} triggers`,
      });
    }
    if (triggerChanges.modified.length > 0) {
      changes.push({
        type: 'modified',
        path: '/triggers',
        description: `Modified ${triggerChanges.modified.length} triggers`,
      });
    }
    if (triggerChanges.removed.length > 0) {
      changes.push({
        type: 'removed',
        path: '/triggers',
        description: `Removed ${triggerChanges.removed.length} triggers`,
      });
    }

    return changes;
  }

  private compareParameters(
    oldParams: PipelineParameter[],
    newParams: PipelineParameter[]
  ) {
    const added: PipelineParameter[] = [];
    const modified: { name: string; old: PipelineParameter; new: PipelineParameter }[] = [];
    const removed: PipelineParameter[] = [];

    const oldMap = new Map(oldParams.map(p => [p.name, p]));
    const newMap = new Map(newParams.map(p => [p.name, p]));

    // Find added and modified
    for (const [name, newParam] of newMap) {
      const oldParam = oldMap.get(name);
      if (!oldParam) {
        added.push(newParam);
      } else if (JSON.stringify(oldParam) !== JSON.stringify(newParam)) {
        modified.push({ name, old: oldParam, new: newParam });
      }
    }

    // Find removed
    for (const [name, oldParam] of oldMap) {
      if (!newMap.has(name)) {
        removed.push(oldParam);
      }
    }

    return { added, modified, removed };
  }

  private compareJobs(
    oldJobs: PipelineJob[],
    newJobs: PipelineJob[]
  ) {
    const added: PipelineJob[] = [];
    const modified: { id: string; old: PipelineJob; new: PipelineJob }[] = [];
    const removed: PipelineJob[] = [];

    const oldMap = new Map(oldJobs.map(j => [j.id, j]));
    const newMap = new Map(newJobs.map(j => [j.id, j]));

    // Find added and modified
    for (const [id, newJob] of newMap) {
      const oldJob = oldMap.get(id);
      if (!oldJob) {
        added.push(newJob);
      } else if (JSON.stringify(oldJob) !== JSON.stringify(newJob)) {
        modified.push({ id, old: oldJob, new: newJob });
      }
    }

    // Find removed
    for (const [id, oldJob] of oldMap) {
      if (!newMap.has(id)) {
        removed.push(oldJob);
      }
    }

    return { added, modified, removed };
  }

  private compareTriggers(
    oldTriggers: PipelineTrigger[],
    newTriggers: PipelineTrigger[]
  ) {
    const added: PipelineTrigger[] = [];
    const modified: { type: string; old: PipelineTrigger; new: PipelineTrigger }[] = [];
    const removed: PipelineTrigger[] = [];

    const oldMap = new Map(oldTriggers.map(t => [t.type, t]));
    const newMap = new Map(newTriggers.map(t => [t.type, t]));

    // Find added and modified
    for (const [type, newTrigger] of newMap) {
      const oldTrigger = oldMap.get(type);
      if (!oldTrigger) {
        added.push(newTrigger);
      } else if (JSON.stringify(oldTrigger) !== JSON.stringify(newTrigger)) {
        modified.push({ type, old: oldTrigger, new: newTrigger });
      }
    }

    // Find removed
    for (const [type, oldTrigger] of oldMap) {
      if (!newMap.has(type)) {
        removed.push(oldTrigger);
      }
    }

    return { added, modified, removed };
  }

  private validateStatusTransition(from: VersionStatus, to: VersionStatus) {
    const validTransitions: Record<VersionStatus, VersionStatus[]> = {
      [VersionStatus.DRAFT]: [VersionStatus.ACTIVE, VersionStatus.ARCHIVED],
      [VersionStatus.ACTIVE]: [VersionStatus.DEPRECATED, VersionStatus.ARCHIVED],
      [VersionStatus.DEPRECATED]: [VersionStatus.ARCHIVED],
      [VersionStatus.ARCHIVED]: [],
    };

    if (!validTransitions[from].includes(to)) {
      throw new VersionValidationError(
        `Invalid status transition from ${from} to ${to}`
      );
    }
  }
} 