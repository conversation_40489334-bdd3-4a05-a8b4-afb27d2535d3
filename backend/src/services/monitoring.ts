import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import os from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface SystemMetrics {
  cpu: {
    usage: number;
    load: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
}

interface ServiceHealth {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency: number;
  lastChecked: Date;
  error?: string;
}

class MonitoringService {
  private static instance: MonitoringService;
  private prisma: PrismaClient;
  private metrics: Map<string, SystemMetrics>;
  private healthChecks: Map<string, ServiceHealth>;
  private lastNetworkStats: { bytesIn: number; bytesOut: number };

  private constructor() {
    this.prisma = new PrismaClient();
    this.metrics = new Map();
    this.healthChecks = new Map();
    this.lastNetworkStats = { bytesIn: 0, bytesOut: 0 };
  }

  public static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  // System Metrics
  public async collectSystemMetrics(): Promise<SystemMetrics> {
    try {
      const cpuUsage = await this.getCPUUsage();
      const loadAvg = os.loadavg();
      const memory = this.getMemoryUsage();
      const disk = await this.getDiskUsage();
      const network = await this.getNetworkStats();

      const metrics: SystemMetrics = {
        cpu: {
          usage: cpuUsage,
          load: loadAvg,
        },
        memory,
        disk,
        network,
      };

      this.metrics.set(new Date().toISOString(), metrics);
      await this.storeMetrics(metrics);

      return metrics;
    } catch (error) {
      logger.error('Failed to collect system metrics:', error);
      throw error;
    }
  }

  private async getCPUUsage(): Promise<number> {
    const { stdout } = await execAsync('top -l 1 | grep "CPU usage"');
    const match = stdout.match(/(\d+\.\d+)% user/);
    return match ? parseFloat(match[1]) : 0;
  }

  private getMemoryUsage(): { total: number; used: number; free: number } {
    const total = os.totalmem();
    const free = os.freemem();
    return {
      total,
      used: total - free,
      free,
    };
  }

  private async getDiskUsage(): Promise<{ total: number; used: number; free: number }> {
    const { stdout } = await execAsync('df -k / | tail -n 1');
    const [, total, used, free] = stdout.split(/\s+/);
    return {
      total: parseInt(total) * 1024,
      used: parseInt(used) * 1024,
      free: parseInt(free) * 1024,
    };
  }

  private async getNetworkStats(): Promise<{ bytesIn: number; bytesOut: number }> {
    const { stdout } = await execAsync('netstat -ib | grep -e "^en0"');
    const [, , , bytesIn, , bytesOut] = stdout.split(/\s+/);
    const current = {
      bytesIn: parseInt(bytesIn),
      bytesOut: parseInt(bytesOut),
    };

    const stats = {
      bytesIn: current.bytesIn - this.lastNetworkStats.bytesIn,
      bytesOut: current.bytesOut - this.lastNetworkStats.bytesOut,
    };

    this.lastNetworkStats = current;
    return stats;
  }

  private async storeMetrics(metrics: SystemMetrics): Promise<void> {
    // systemMetrics model doesn't exist - storing in memory only
    // In a real implementation, you would create a proper metrics table
    logger.info('Storing metrics', { metrics });
  }

  // Health Checks
  public async checkServiceHealth(serviceName: string): Promise<ServiceHealth> {
    try {
      const startTime = Date.now();
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      let error: string | undefined;

      switch (serviceName) {
        case 'database':
          await this.checkDatabaseHealth();
          break;
        case 'redis':
          await this.checkRedisHealth();
          break;
        case 'api':
          await this.checkAPIHealth();
          break;
        default:
          throw new Error(`Unknown service: ${serviceName}`);
      }

      const latency = Date.now() - startTime;
      if (latency > 1000) {
        status = 'degraded';
      }

      const health: ServiceHealth = {
        name: serviceName,
        status,
        latency,
        lastChecked: new Date(),
        error,
      };

      this.healthChecks.set(serviceName, health);
      await this.storeHealthCheck(health);

      return health;
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Unknown error occurred';
      const health: ServiceHealth = {
        name: serviceName,
        status: 'unhealthy',
        latency: 0,
        lastChecked: new Date(),
        error,
      };

      this.healthChecks.set(serviceName, health);
      await this.storeHealthCheck(health);

      return health;
    }
  }

  private async checkDatabaseHealth(): Promise<void> {
    await this.prisma.$queryRaw`SELECT 1`;
  }

  private async checkRedisHealth(): Promise<void> {
    // Implement Redis health check
  }

  private async checkAPIHealth(): Promise<void> {
    // Implement API health check
  }

  private async storeHealthCheck(health: ServiceHealth): Promise<void> {
    // serviceHealth model doesn't exist - storing in memory only
    // In a real implementation, you would create a proper health check table
    logger.info('Storing health check', { health });
  }

  // Alerts
  public async checkAlerts(): Promise<void> {
    const metrics = await this.getLatestMetrics();
    if (!metrics) return;

    const alerts = [];

    // CPU Usage Alert
    if (metrics.cpu.usage > 90) {
      alerts.push({
        type: 'CPU_USAGE_HIGH',
        message: `CPU usage is at ${metrics.cpu.usage}%`,
        severity: 'high',
      });
    }

    // Memory Usage Alert
    const memoryUsagePercent = (metrics.memory.used / metrics.memory.total) * 100;
    if (memoryUsagePercent > 90) {
      alerts.push({
        type: 'MEMORY_USAGE_HIGH',
        message: `Memory usage is at ${memoryUsagePercent.toFixed(2)}%`,
        severity: 'high',
      });
    }

    // Disk Usage Alert
    const diskUsagePercent = (metrics.disk.used / metrics.disk.total) * 100;
    if (diskUsagePercent > 90) {
      alerts.push({
        type: 'DISK_USAGE_HIGH',
        message: `Disk usage is at ${diskUsagePercent.toFixed(2)}%`,
        severity: 'high',
      });
    }

    // Store and notify alerts
    for (const alert of alerts) {
      await this.storeAlert(alert);
      await this.notifyAlert(alert);
    }
  }

  private async getLatestMetrics(): Promise<SystemMetrics | null> {
    const latest = Array.from(this.metrics.entries()).pop();
    return latest ? latest[1] : null;
  }

  private async storeAlert(alert: any): Promise<void> {
    // Store alert using the Alert model
    await this.prisma.alert.create({
      data: {
        name: alert.type,
        description: alert.message,
        type: 'monitoring',
        severity: alert.severity,
        status: 'active',
        conditions: {},
        actions: {},
      },
    });
  }

  private async notifyAlert(alert: any): Promise<void> {
    // Implement alert notification (e.g., email, Slack, etc.)
    logger.warn('Alert:', alert);
  }

  // Cleanup
  public async cleanupOldMetrics(retentionDays: number): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    // Clean up old alerts
    await this.prisma.alert.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
      },
    });

    logger.info('Cleaned up old metrics and health checks', { cutoffDate });
  }
}

export { MonitoringService, SystemMetrics, ServiceHealth }; 