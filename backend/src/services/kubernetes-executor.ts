import * as k8s from '@kubernetes/client-node';
import { logger } from '../utils/logger';
import { ActivityService } from './activity';

interface KubernetesConfig {
  namespace: string;
  image: string;
  command?: string[];
  args?: string[];
  env?: Record<string, string>;
  volumes?: {
    name: string;
    mountPath: string;
    type: 'emptyDir' | 'hostPath' | 'configMap' | 'secret';
    config?: any;
  }[];
  resources?: {
    requests?: {
      cpu?: string;
      memory?: string;
    };
    limits?: {
      cpu?: string;
      memory?: string;
    };
  };
  serviceAccount?: string;
  nodeSelector?: Record<string, string>;
  tolerations?: k8s.V1Toleration[];
  affinity?: k8s.V1Affinity;
}

class KubernetesExecutor {
  private static instance: KubernetesExecutor;
  private kc: k8s.KubeConfig;
  private k8sApi: k8s.CoreV1Api;
  private batchApi: k8s.BatchV1Api;
  private activityService: ActivityService;

  private constructor() {
    this.kc = new k8s.KubeConfig();
    this.kc.loadFromDefault();
    this.k8sApi = this.kc.makeApiClient(k8s.CoreV1Api);
    this.batchApi = this.kc.makeApiClient(k8s.BatchV1Api);
    this.activityService = ActivityService.getInstance();
  }

  public static getInstance(): KubernetesExecutor {
    if (!KubernetesExecutor.instance) {
      KubernetesExecutor.instance = new KubernetesExecutor();
    }
    return KubernetesExecutor.instance;
  }

  public async executeStep(
    config: KubernetesConfig,
    userId: string,
    projectId: string,
    pipelineId: string
  ): Promise<{ success: boolean; output: string; error?: string }> {
    try {
      // Create job
      const job = await this.createJob(config);

      // Wait for job completion
      const result = await this.waitForJobCompletion(job.metadata!.name!, config.namespace);

      // Get job logs
      const logs = await this.getJobLogs(job.metadata!.name!, config.namespace);

      // Clean up job
      await this.cleanupJob(job.metadata!.name!, config.namespace);

      // Log activity
      await this.activityService.logActivity({
        type: 'JOB_COMPLETED',
        userId,
        projectId,
        resourceId: pipelineId,
        details: {
          image: config.image,
          jobName: job.metadata!.name,
        },
      });

      return {
        success: result.succeeded > 0,
        output: logs,
        error: result.failed > 0 ? 'Job failed' : undefined,
      };
    } catch (error: any) {
      logger.error('Kubernetes execution failed:', error);

      // Log activity
      await this.activityService.logActivity({
        type: 'JOB_FAILED',
        userId,
        projectId,
        resourceId: pipelineId,
        details: {
          image: config.image,
          error: error.message || 'Unknown error',
        },
      });

      return {
        success: false,
        output: '',
        error: error.message || 'Unknown error',
      };
    }
  }

  private async createJob(config: KubernetesConfig): Promise<k8s.V1Job> {
    const job: k8s.V1Job = {
      apiVersion: 'batch/v1',
      kind: 'Job',
      metadata: {
        generateName: 'pipeline-job-',
        namespace: config.namespace,
      },
      spec: {
        template: {
          spec: {
            containers: [
              {
                name: 'main',
                image: config.image,
                command: config.command,
                args: config.args,
                env: Object.entries(config.env || {}).map(([name, value]) => ({
                  name,
                  value,
                })),
                volumeMounts: config.volumes?.map((volume) => ({
                  name: volume.name,
                  mountPath: volume.mountPath,
                })),
                resources: config.resources,
              },
            ],
            volumes: config.volumes?.map((volume) => ({
              name: volume.name,
              ...this.createVolume(volume),
            })),
            serviceAccountName: config.serviceAccount,
            nodeSelector: config.nodeSelector,
            tolerations: config.tolerations,
            affinity: config.affinity,
            restartPolicy: 'Never',
          },
        },
      },
    };

    const response = await this.batchApi.createNamespacedJob({
      namespace: config.namespace,
      body: job
    });
    return response as any;
  }

  private createVolume(volume: NonNullable<KubernetesConfig['volumes']>[number]): any {
    switch (volume.type) {
      case 'emptyDir':
        return { emptyDir: {} };
      case 'hostPath':
        return { hostPath: volume.config };
      case 'configMap':
        return { configMap: volume.config };
      case 'secret':
        return { secret: volume.config };
      default:
        throw new Error(`Unsupported volume type: ${volume.type}`);
    }
  }

  private async waitForJobCompletion(jobName: string, namespace: string): Promise<{ succeeded: number; failed: number }> {
    while (true) {
      const job = await this.batchApi.readNamespacedJob({
        name: jobName,
        namespace: namespace
      });
      const status = (job as any).status;

      if (status?.succeeded || status?.failed) {
        return {
          succeeded: status.succeeded || 0,
          failed: status.failed || 0,
        };
      }

      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  }

  private async getJobLogs(jobName: string, namespace: string): Promise<string> {
    const pods = await this.k8sApi.listNamespacedPod({
      namespace,
      labelSelector: `job-name=${jobName}`
    });

    if (!(pods as any).items?.length) {
      return '';
    }

    const pod = (pods as any).items[0];
    const response = await this.k8sApi.readNamespacedPodLog({
      name: pod.metadata!.name!,
      namespace
    });

    return response as any;
  }

  private async cleanupJob(jobName: string, namespace: string): Promise<void> {
    await this.batchApi.deleteNamespacedJob({
      name: jobName,
      namespace
    });
  }
}

export { KubernetesExecutor, KubernetesConfig }; 