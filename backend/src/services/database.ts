import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

interface UserCreateInput {
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  password: string;
  role?: string;
  isActive?: boolean;
  settings?: any;
}

interface UserUpdateInput {
  email?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  password?: string;
  role?: string;
  isActive?: boolean;
  settings?: any;
}

interface ProjectCreateInput {
  name: string;
  slug: string;
  description?: string;
  settings?: any;
  owner: { connect: { id: string } };
}

interface ProjectUpdateInput {
  name?: string;
  slug?: string;
  description?: string;
  settings?: any;
  members?: { connect?: { id: string }[]; disconnect?: { id: string }[] };
}

interface PipelineCreateInput {
  name: string;
  description?: string;
  config: any;
  owner: { connect: { id: string } };
  project: { connect: { id: string } };
}

interface PipelineUpdateInput {
  name?: string;
  description?: string;
  config?: any;
}

interface JobCreateInput {
  name: string;
  description?: string;
  config: any;
  status?: string;
  project?: { connect: { id: string } };
  pipeline?: { connect: { id: string } };
  pipelineRun?: { connect: { id: string } };
}

interface JobUpdateInput {
  name?: string;
  description?: string;
  config?: any;
  status?: string;
  startedAt?: Date;
  completedAt?: Date;
  output?: any;
}

interface ActivityCreateInput {
  type: string;
  description: string;
  user: { connect: { id: string } };
  project?: { connect: { id: string } };
  pipeline?: { connect: { id: string } };
}

class DatabaseService {
  private static instance: DatabaseService;
  private prisma: PrismaClient;

  public get client() {
    return this.prisma;
  }

  private constructor() {
    this.prisma = new PrismaClient();
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public static async initialize(): Promise<void> {
    try {
      const instance = DatabaseService.getInstance();
      await instance.prisma.$connect();
    } catch (error) {
      logger.error('Failed to initialize database connection:', error);
      throw new Error('Connection failed');
    }
  }

  public static getClient(): PrismaClient {
    return DatabaseService.getInstance().prisma;
  }

  public static async close(): Promise<void> {
    try {
      const instance = DatabaseService.getInstance();
      await instance.prisma.$disconnect();
    } catch (error) {
      logger.error('Failed to close database connection:', error);
      throw new Error('Disconnection failed');
    }
  }

  public static async healthCheck(): Promise<boolean> {
    try {
      const instance = DatabaseService.getInstance();
      await instance.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  // User Management
  public async createUser(data: UserCreateInput) {
    return this.prisma.user.create({ data });
  }

  public async updateUser(id: string, data: UserUpdateInput) {
    return this.prisma.user.update({
      where: { id },
      data,
    });
  }

  public async getUserById(id: string) {
    return this.prisma.user.findUnique({
      where: { id },
    });
  }

  public async getUserByEmail(email: string) {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  public async getUserProjects(userId: string) {
    return this.prisma.project.findMany({
      where: {
        members: {
          some: {
            userId,
          },
        },
      },
    });
  }

  // Project Management
  public async createProject(data: ProjectCreateInput) {
    return this.prisma.project.create({ data });
  }

  public async updateProject(id: string, data: ProjectUpdateInput) {
    return this.prisma.project.update({
      where: { id },
      data,
    });
  }

  public async getProjectById(id: string) {
    return this.prisma.project.findUnique({
      where: { id },
    });
  }

  // Pipeline Management
  public async createPipeline(data: PipelineCreateInput) {
    return this.prisma.pipeline.create({ data });
  }

  public async updatePipeline(id: string, data: PipelineUpdateInput) {
    return this.prisma.pipeline.update({
      where: { id },
      data,
    });
  }

  public async getPipelineById(id: string) {
    return this.prisma.pipeline.findUnique({
      where: { id },
    });
  }

  // Job Management
  public async createJob(data: JobCreateInput) {
    return this.prisma.job.create({
      data: {
        ...data,
        status: (data.status as any) || 'PENDING',
        startedAt: new Date() // Add required startedAt field
      }
    });
  }

  public async updateJob(id: string, data: JobUpdateInput) {
    return this.prisma.job.update({
      where: { id },
      data: {
        ...data,
        status: data.status as any // Cast to proper enum type
      },
    });
  }

  public async getJobById(id: string) {
    return this.prisma.job.findUnique({
      where: { id },
    });
  }

  // Activity Logging
  public async createActivity(data: ActivityCreateInput) {
    return this.prisma.$transaction(async (tx) => {
      const activity = await tx.auditLog.create({
        data: {
          userId: data.user.connect.id,
          action: 'CREATE',
          resource: 'SYSTEM' as any, // AUDIT_LOG doesn't exist in enum
          details: {
            type: data.type,
            description: data.description,
            projectId: data.project?.connect.id,
            pipelineId: data.pipeline?.connect.id,
          },
        },
      });
      return activity;
    });
  }
}

// Export prisma client instance for direct use
export const prisma = DatabaseService.getClient();

export {
  DatabaseService,
  UserCreateInput,
  UserUpdateInput,
  ProjectCreateInput,
  ProjectUpdateInput,
  PipelineCreateInput,
  PipelineUpdateInput,
  JobCreateInput,
  JobUpdateInput,
  ActivityCreateInput,
};
