import { PrismaClient } from '@prisma/client';
import { createCipher<PERSON>, createDecipheriv, randomBytes, Cipheriv, Decipheriv } from 'crypto';
import { logger } from '../lib/logger';

interface SecretConfig {
  encryptionKey: string;
  algorithm?: string;
}

export class SecretsService {
  private static instance: SecretsService;
  private prisma: PrismaClient;
  private config: SecretConfig;
  private readonly algorithm: string;

  private constructor(config: SecretConfig) {
    this.config = config;
    this.prisma = new PrismaClient();
    this.algorithm = config.algorithm || 'aes-256-gcm';
  }

  public static getInstance(config?: SecretConfig): SecretsService {
    if (!SecretsService.instance && config) {
      SecretsService.instance = new SecretsService(config);
    }
    return SecretsService.instance;
  }

  /**
   * Create a new secret
   */
  async createSecret(
    projectId: string,
    key: string,
    value: string,
    description?: string
  ): Promise<void> {
    try {
      let encryptedValue: string;
      try {
        encryptedValue = this.encrypt(value);
      } catch (encryptError) {
        logger.error('Failed to encrypt secret', {
          projectId,
          key,
          error: encryptError as Error,
        });
        throw new Error('Failed to encrypt secret');
      }
      
      await this.prisma.secret.create({
        data: {
          name: key, // Required field
          project: projectId,
          key,
          value: encryptedValue,
          description,
        },
      });

      logger.info('Secret created successfully', {
        projectId,
        key,
      });
    } catch (error) {
      logger.error('Failed to create secret', {
        projectId,
        key,
        error: error as Error,
      });
      throw new Error('Failed to create secret');
    }
  }

  /**
   * Get a secret value
   */
  async getSecret(projectId: string, key: string): Promise<string> {
    try {
      const secret = await this.prisma.secret.findFirst({
        where: {
          project: projectId,
          key: key,
        },
      });

      if (!secret) {
        logger.warn('Secret not found', {
          projectId,
          key,
        });
        throw new Error('Secret not found');
      }

      const decryptedValue = this.decrypt(secret.value);

      logger.info('Secret retrieved successfully', {
        projectId,
        key,
      });

      return decryptedValue;
    } catch (error) {
      if ((error as Error).message === 'Secret not found') {
        throw error;
      }
      // Check if it's a decryption error
      if ((error as Error).message === 'Failed to decrypt secret') {
        throw error;
      }
      logger.error('Failed to get secret', {
        projectId,
        key,
        error: error as Error,
      });
      throw new Error('Failed to get secret');
    }
  }

  /**
   * List all secrets for a project
   */
  async listSecrets(projectId: string): Promise<Array<{ key: string; description?: string | null }>> {
    try {
      const secrets = await this.prisma.secret.findMany({
        where: { project: projectId },
        select: {
          key: true,
          description: true,
        },
      });

      logger.info('Secrets listed successfully', {
        projectId,
        count: secrets.length,
      });

      return secrets;
    } catch (error) {
      logger.error('Failed to list secrets', {
        projectId,
        error: error as Error,
      });
      throw new Error('Failed to list secrets');
    }
  }

  /**
   * Update a secret
   */
  async updateSecret(
    projectId: string,
    key: string,
    value: string,
    description?: string
  ): Promise<void> {
    try {
      const encryptedValue = this.encrypt(value);
      
      const secret = await this.prisma.secret.findFirst({
        where: {
          project: projectId,
          key: key,
        },
      });

      if (!secret) {
        throw new Error('Secret not found');
      }

      await this.prisma.secret.update({
        where: {
          id: secret.id,
        },
        data: {
          value: encryptedValue,
          description,
        },
      });

      logger.info('Secret updated successfully', {
        projectId,
        key,
      });
    } catch (error) {
      logger.error('Failed to update secret', {
        projectId,
        key,
        error: error as Error,
      });
      throw new Error('Failed to update secret');
    }
  }

  /**
   * Delete a secret
   */
  async deleteSecret(projectId: string, key: string): Promise<void> {
    try {
      const secret = await this.prisma.secret.findFirst({
        where: {
          project: projectId,
          key: key,
        },
      });

      if (!secret) {
        throw new Error('Secret not found');
      }

      await this.prisma.secret.delete({
        where: {
          id: secret.id,
        },
      });

      logger.info('Secret deleted successfully', {
        projectId,
        key,
      });
    } catch (error) {
      logger.error('Failed to delete secret', {
        projectId,
        key,
        error: error as Error,
      });
      throw new Error('Failed to delete secret');
    }
  }

  /**
   * Encrypt a value
   */
  private encrypt(value: string): string {
    try {
      const iv = randomBytes(16);
      const cipher = createCipheriv(
        this.algorithm,
        Buffer.from(this.config.encryptionKey, 'hex'),
        iv
      ) as Cipheriv & { getAuthTag(): Buffer };

      let encrypted = cipher.update(value, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      const authTag = cipher.getAuthTag();

      // Combine IV, encrypted data, and auth tag and encode as base64
      const combined = `${iv.toString('hex')}:${encrypted}:${authTag.toString('hex')}`;
      return Buffer.from(combined).toString('base64');
    } catch (error) {
      logger.error('Failed to encrypt secret', {
        error: error as Error,
      });
      throw new Error('Failed to encrypt secret');
    }
  }

  /**
   * Decrypt a value
   */
  private decrypt(encryptedValue: string): string {
    try {
      // Decode from base64 first
      const combined = Buffer.from(encryptedValue, 'base64').toString();
      const [ivHex, encrypted, authTagHex] = combined.split(':');
      
      const iv = Buffer.from(ivHex, 'hex');
      const authTag = Buffer.from(authTagHex, 'hex');
      
      const decipher = createDecipheriv(
        this.algorithm,
        Buffer.from(this.config.encryptionKey, 'hex'),
        iv
      ) as Decipheriv & { setAuthTag(tag: Buffer): void };

      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      logger.error('Failed to decrypt secret', {
        error: error as Error,
      });
      throw new Error('Failed to decrypt secret');
    }
  }
} 