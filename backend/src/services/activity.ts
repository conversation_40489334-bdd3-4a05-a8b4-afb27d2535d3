import { PrismaClient, Prisma } from '@prisma/client';
import { logger } from '../utils/logger';

interface ActivityCreateInput {
  type: string;
  action: string;
  resource: string;
  resourceId?: string;
  userId: string;
  projectId?: string;
  metadata?: any;
}

interface ActivityStats {
  [key: string]: number;
}

class ActivityService {
  private static instance: ActivityService;
  private prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient();
  }

  public static getInstance(): ActivityService {
    if (!ActivityService.instance) {
      ActivityService.instance = new ActivityService();
    }
    return ActivityService.instance;
  }

  // Activity Logging
  public async logActivity(input: ActivityCreateInput): Promise<void> {
    try {
      await this.prisma.activity.create({
        data: {
          type: input.type,
          action: input.action,
          resource: input.resource,
          resourceId: input.resourceId,
          userId: input.userId,
          projectId: input.projectId,
          metadata: input.metadata,
        },
      });
    } catch (error) {
      logger.error('Failed to log activity:', error);
    }
  }

  // Query Methods
  public async getUserActivities(userId: string, limit = 50): Promise<any[]> {
    return this.prisma.activity.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
          },
        },
      },
    });
  }

  public async getProjectActivities(projectId: string, limit = 50): Promise<any[]> {
    return this.prisma.activity.findMany({
      where: { projectId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
          },
        },
      },
    });
  }

  public async getPipelineActivities(pipelineId: string, limit = 50): Promise<any[]> {
    return this.prisma.activity.findMany({
      where: { resourceId: pipelineId, resource: 'pipeline' },
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
          },
        },
      },
    });
  }

  public async getActivitiesByType(type: string, limit = 50): Promise<any[]> {
    return this.prisma.activity.findMany({
      where: { type },
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
          },
        },
      },
    });
  }

  // Analytics
  public async getActivityStats(startDate: Date, endDate: Date): Promise<ActivityStats> {
    const activities = await this.prisma.activity.groupBy({
      by: ['type'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: true,
    });

    return activities.reduce((acc: ActivityStats, activity: any) => {
      acc[activity.type] = activity._count;
      return acc;
    }, {});
  }

  public async getUserActivityStats(userId: string, startDate: Date, endDate: Date): Promise<ActivityStats> {
    const activities = await this.prisma.activity.groupBy({
      by: ['type'],
      where: {
        userId,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: true,
    });

    return activities.reduce((acc: ActivityStats, activity: any) => {
      acc[activity.type] = activity._count;
      return acc;
    }, {});
  }

  public async getProjectActivityStats(projectId: string, startDate: Date, endDate: Date): Promise<ActivityStats> {
    const activities = await this.prisma.activity.groupBy({
      by: ['type'],
      where: {
        projectId,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: true,
    });

    return activities.reduce((acc: ActivityStats, activity: any) => {
      acc[activity.type] = activity._count;
      return acc;
    }, {});
  }

  // Cleanup
  public async cleanupOldActivities(retentionDays: number): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    await this.prisma.activity.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
      },
    });
  }
}

export { ActivityService, ActivityCreateInput, ActivityStats }; 