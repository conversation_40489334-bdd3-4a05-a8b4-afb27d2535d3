import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import crypto from 'crypto';
import { URL } from 'url';
import { RateLimiter } from 'limiter';
import { EventEmitter } from 'events';
import { parse } from 'dotenv';

interface Webhook {
  id: string;
  name: string;
  path: string;
  method: string;
  headers: Record<string, string> | null;
  body: any | null;
  secret: string;
  isActive: boolean;
  pipelineId: string;
  rateLimit: number;
  timeout: number;
  allowedIps: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface WebhookCreateInput {
  name: string;
  path: string;
  method: string;
  headers?: Record<string, string>;
  body?: any;
  pipelineId: string;
  secret?: string;
  rateLimit?: number;
  timeout?: number;
  allowedIps?: string[];
  template?: string;
}

interface WebhookUpdateInput {
  name?: string;
  path?: string;
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  isActive?: boolean;
  secret?: string;
  rateLimit?: number;
  timeout?: number;
  allowedIps?: string[];
  template?: string;
}

interface WebhookRetryConfig {
  maxRetries: number;
  retryDelay: number;
  backoffFactor: number;
}

interface WebhookEvent {
  id: string;
  webhookId: string;
  status: 'success' | 'failed' | 'pending';
  responseCode?: number;
  responseBody?: string;
  error?: string;
  attempt: number;
  duration: number;
  createdAt: Date;
}

interface WebhookStats {
  totalDeliveries: number;
  successRate: number;
  averageResponseTime: number;
  errorRate: number;
  lastDelivery?: Date;
}

interface WebhookQueueItem {
  id: string;
  webhookId: string;
  payload: any;
  priority: number;
  retryCount: number;
  nextAttempt: Date;
  createdAt: Date;
}

interface WebhookHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: Date;
  responseTime: number;
  error?: string;
}

// Use the actual PrismaClient since webhook models exist

class WebhookService extends EventEmitter {
  private static instance: WebhookService;
  private prisma: PrismaClient;
  private rateLimiters: Map<string, RateLimiter>;
  private queue: WebhookQueueItem[];
  private healthChecks: Map<string, WebhookHealth>;
  private readonly DEFAULT_RETRY_CONFIG: WebhookRetryConfig = {
    maxRetries: 3,
    retryDelay: 1000,
    backoffFactor: 2,
  };
  private readonly DEFAULT_TIMEOUT = 10000;
  private readonly DEFAULT_RATE_LIMIT = 100;
  private readonly QUEUE_PROCESS_INTERVAL = 5000; // 5 seconds
  private readonly HEALTH_CHECK_INTERVAL = 300000; // 5 minutes

  private constructor() {
    super();
    this.prisma = new PrismaClient();
    this.rateLimiters = new Map();
    this.queue = [];
    this.healthChecks = new Map();
    this.startQueueProcessor();
    this.startHealthChecks();
  }

  public static getInstance(): WebhookService {
    if (!WebhookService.instance) {
      WebhookService.instance = new WebhookService();
    }
    return WebhookService.instance;
  }

  public async createWebhook(data: WebhookCreateInput): Promise<Webhook> {
    // Validate webhook configuration
    this.validateWebhookConfig(data);

    const webhook = await this.prisma.webhook.create({
      data: {
        url: data.path || data.url || '',
        events: data.events || [],
        secret: data.secret || this.generateSecret(),
        active: true,
        timeout: data.timeout || this.DEFAULT_TIMEOUT,
      },
    });

    logger.info(`Created webhook ${webhook.id}`, {
      webhookId: webhook.id,
      url: webhook.url,
    });

    return {
      ...webhook,
      name: data.name || 'Webhook',
      path: webhook.url,
      method: data.method || 'POST',
      headers: data.headers || {},
      body: data.body || {},
      pipelineId: data.pipelineId,
      rateLimit: data.rateLimit || this.DEFAULT_RATE_LIMIT,
      allowedIps: data.allowedIps || [],
    } as any;
  }

  public async updateWebhook(
    id: string,
    data: WebhookUpdateInput
  ): Promise<Webhook> {
    // Validate webhook configuration if path or method is being updated
    if (data.path || data.method) {
      this.validateWebhookConfig(data as WebhookCreateInput);
    }

    const webhook = await this.prisma.webhook.update({
      where: { id },
      data: {
        url: data.path || data.url,
        events: data.events,
        secret: data.secret,
        active: data.active,
        timeout: data.timeout,
      },
    });

    logger.info(`Updated webhook ${webhook.id}`, {
      webhookId: webhook.id,
    });

    return {
      ...webhook,
      name: data.name || 'Webhook',
      path: webhook.url,
      method: data.method || 'POST',
      headers: data.headers || {},
      body: data.body || {},
      pipelineId: data.pipelineId,
      rateLimit: data.rateLimit || this.DEFAULT_RATE_LIMIT,
      allowedIps: data.allowedIps || [],
    } as any;
  }

  public async deleteWebhook(id: string): Promise<void> {
    await this.prisma.webhook.delete({
      where: { id },
    });

    logger.info(`Deleted webhook ${id}`);
  }

  public async getWebhookById(id: string): Promise<Webhook | null> {
    return this.prisma.webhook.findUnique({
      where: { id },
    });
  }

  public async getWebhookByPipelineId(pipelineId: string): Promise<Webhook | null> {
    // Since pipelineId doesn't exist in schema, return null for now
    return null;
  }

  public async validateWebhookSignature(
    webhookId: string,
    payload: string,
    signature: string
  ): Promise<boolean> {
    try {
      const webhook = await this.getWebhookById(webhookId);
      if (!webhook) {
        logger.warn(`Webhook not found: ${webhookId}`);
        return false;
      }

      if (!webhook.secret) {
        logger.warn(`Webhook ${webhookId} has no secret configured`);
        return false;
      }

      const hmac = crypto.createHmac('sha256', webhook.secret);
      const calculatedSignature = hmac.update(payload).digest('hex');

      const isValid = calculatedSignature === signature;
      if (!isValid) {
        logger.warn(`Invalid webhook signature for webhook ${webhookId}`);
      }

      return isValid;
    } catch (error) {
      logger.error(`Error validating webhook signature: ${error}`);
      return false;
    }
  }

  public async retryWebhook(
    webhookId: string,
    payload: any,
    config: Partial<WebhookRetryConfig> = {}
  ): Promise<boolean> {
    const retryConfig = { ...this.DEFAULT_RETRY_CONFIG, ...config };
    let retryCount = 0;
    let delay = retryConfig.retryDelay;

    while (retryCount < retryConfig.maxRetries) {
      try {
        const webhook = await this.getWebhookById(webhookId);
        if (!webhook) {
          throw new Error(`Webhook not found: ${webhookId}`);
        }

        // Make the webhook request
        const response = await fetch(webhook.url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });

        if (response.ok) {
          logger.info(`Webhook ${webhookId} retry successful`, {
            retryCount,
            statusCode: response.status,
          });
          return true;
        }

        throw new Error(`Webhook request failed with status ${response.status}`);
      } catch (error) {
        retryCount++;
        if (retryCount === retryConfig.maxRetries) {
          logger.error(`Webhook ${webhookId} retry failed after ${retryCount} attempts`, {
            error,
            retryCount,
          });
          return false;
        }

        // Wait before next retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= retryConfig.backoffFactor;
      }
    }

    return false;
  }

  public async testWebhook(webhookId: string): Promise<{
    success: boolean;
    responseCode?: number;
    responseBody?: string;
    error?: string;
  }> {
    try {
      const webhook = await this.getWebhookById(webhookId);
      if (!webhook) {
        throw new Error(`Webhook not found: ${webhookId}`);
      }

      const startTime = Date.now();
      const response = await fetch(webhook.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      const duration = Date.now() - startTime;
      const responseBody = await response.text();

      // Record the test event (using notification as substitute)
      await this.prisma.notification.create({
        data: {
          channelId: webhookId,
          event: 'SYSTEM_ALERT' as any,
          payload: {
            status: response.ok ? 'success' : 'failed',
            responseCode: response.status,
            responseBody,
            attempt: 1,
            duration,
          },
          status: response.ok ? 'sent' : 'failed',
          name: 'Webhook-Test',
        },
      });

      return {
        success: response.ok,
        responseCode: response.status,
        responseBody,
      };
    } catch (error) {
      // Record the failed event (using notification as substitute)
      await this.prisma.notification.create({
        data: {
          channelId: webhookId,
          event: 'SYSTEM_ALERT' as any,
          payload: {
            status: 'failed',
            error: error instanceof Error ? error.message : String(error),
            attempt: 1,
            duration: 0,
          },
          status: 'failed',
          name: 'Webhook-Test-Failed',
        },
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  public async getWebhookStats(webhookId: string): Promise<WebhookStats> {
    // Use notifications as substitute for webhookEvent
    const events = await this.prisma.notification.findMany({
      where: { channelId: webhookId },
      orderBy: { createdAt: 'desc' },
    });

    if (events.length === 0) {
      return {
        totalDeliveries: 0,
        successRate: 0,
        averageResponseTime: 0,
        errorRate: 0,
      };
    }

    const successfulEvents = events.filter(e => e.status === 'sent');
    const failedEvents = events.filter(e => e.status === 'failed');
    const totalDuration = events.reduce((sum, e) => sum + ((e.payload as any)?.duration || 0), 0);

    return {
      totalDeliveries: events.length,
      successRate: (successfulEvents.length / events.length) * 100,
      averageResponseTime: totalDuration / events.length,
      errorRate: (failedEvents.length / events.length) * 100,
      lastDelivery: events[0].createdAt,
    };
  }

  public async getWebhookEvents(
    webhookId: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<WebhookEvent[]> {
    // Use notifications as substitute for webhookEvent
    const notifications = await this.prisma.notification.findMany({
      where: { channelId: webhookId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
    });

    return notifications.map(notif => ({
      id: notif.id,
      webhookId,
      event: (notif.payload as any)?.event || 'unknown',
      payload: (notif.payload as any)?.payload || {},
      status: notif.status === 'sent' ? 'success' : 'failed',
      statusCode: (notif.payload as any)?.statusCode || 0,
      responseBody: (notif.payload as any)?.responseBody || '',
      attempt: (notif.payload as any)?.attempt || 1,
      duration: (notif.payload as any)?.duration || 0,
      createdAt: notif.createdAt,
    })) as WebhookEvent[];
  }

  private validateWebhookConfig(config: WebhookCreateInput): void {
    // Validate URL
    try {
      new URL(config.path);
    } catch (error) {
      throw new Error(`Invalid webhook URL: ${config.path}`);
    }

    // Validate HTTP method
    const validMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
    if (!validMethods.includes(config.method.toUpperCase())) {
      throw new Error(`Invalid HTTP method: ${config.method}`);
    }

    // Validate headers if provided
    if (config.headers) {
      for (const [key, value] of Object.entries(config.headers)) {
        if (typeof key !== 'string' || typeof value !== 'string') {
          throw new Error('Invalid header format. Headers must be key-value pairs of strings.');
        }
      }
    }

    // Validate rate limit if provided
    if (config.rateLimit !== undefined && config.rateLimit < 0) {
      throw new Error('Rate limit must be a positive number');
    }

    // Validate timeout if provided
    if (config.timeout !== undefined && config.timeout < 0) {
      throw new Error('Timeout must be a positive number');
    }

    // Validate allowed IPs if provided
    if (config.allowedIps) {
      for (const ip of config.allowedIps) {
        if (!this.isValidIp(ip)) {
          throw new Error(`Invalid IP address: ${ip}`);
        }
      }
    }
  }

  private isValidIp(ip: string): boolean {
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  private generateSecret(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  public async processTemplate(template: string, variables: Record<string, any>): Promise<string> {
    return template.replace(/\{\{([^}]+)\}\}/g, (match, key) => {
      const value = key.split('.').reduce((obj: any, k: string) => obj?.[k], variables);
      return value !== undefined ? String(value) : match;
    });
  }

  public async queueWebhook(webhookId: string, payload: any, priority: number = 0): Promise<void> {
    const queueItem: WebhookQueueItem = {
      id: crypto.randomUUID(),
      webhookId,
      payload,
      priority,
      retryCount: 0,
      nextAttempt: new Date(),
      createdAt: new Date(),
    };

    this.queue.push(queueItem);
    this.queue.sort((a, b) => b.priority - a.priority);
    this.emit('webhookQueued', queueItem);
  }

  private async startQueueProcessor(): Promise<void> {
    setInterval(async () => {
      const now = new Date();
      const itemsToProcess = this.queue.filter(item => item.nextAttempt <= now);

      for (const item of itemsToProcess) {
        try {
          await this.processQueueItem(item);
        } catch (error) {
          logger.error(`Error processing queue item ${item.id}:`, error);
        }
      }
    }, this.QUEUE_PROCESS_INTERVAL);
  }

  private async processQueueItem(item: WebhookQueueItem): Promise<void> {
    const webhook = await this.getWebhookById(item.webhookId);
    if (!webhook) {
      this.queue = this.queue.filter(i => i.id !== item.id);
      return;
    }

    try {
      const result = await this.sendWebhook(webhook, item.payload);
      if (result.success) {
        this.queue = this.queue.filter(i => i.id !== item.id);
        this.emit('webhookProcessed', { item, success: true });
      } else if (item.retryCount < this.DEFAULT_RETRY_CONFIG.maxRetries) {
        item.retryCount++;
        item.nextAttempt = new Date(Date.now() + 
          this.DEFAULT_RETRY_CONFIG.retryDelay * Math.pow(this.DEFAULT_RETRY_CONFIG.backoffFactor, item.retryCount));
        this.emit('webhookRetry', { item, error: result.error });
      } else {
        this.queue = this.queue.filter(i => i.id !== item.id);
        this.emit('webhookFailed', { item, error: result.error });
      }
    } catch (error) {
      logger.error(`Error processing webhook ${item.webhookId}:`, error);
      this.emit('webhookError', { item, error });
    }
  }

  private async startHealthChecks(): Promise<void> {
    setInterval(async () => {
      const webhooks = await this.prisma.webhook.findMany({
        where: { active: true },
      });

      for (const webhook of webhooks) {
        try {
          const startTime = Date.now();
          const response = await fetch(webhook.url, {
            method: 'HEAD',
          });

          const health: WebhookHealth = {
            status: response.ok ? 'healthy' : 'degraded',
            lastCheck: new Date(),
            responseTime: Date.now() - startTime,
          };

          this.healthChecks.set(webhook.id, health);
          this.emit('healthCheck', { webhookId: webhook.id, health });
        } catch (error) {
          const health: WebhookHealth = {
            status: 'unhealthy',
            lastCheck: new Date(),
            responseTime: 0,
            error: error instanceof Error ? error.message : String(error),
          };

          this.healthChecks.set(webhook.id, health);
          this.emit('healthCheck', { webhookId: webhook.id, health });
        }
      }
    }, this.HEALTH_CHECK_INTERVAL);
  }

  public getWebhookHealth(webhookId: string): WebhookHealth | undefined {
    return this.healthChecks.get(webhookId);
  }

  public async getAllWebhookHealth(): Promise<Map<string, WebhookHealth>> {
    return this.healthChecks;
  }

  private async sendWebhook(webhook: Webhook, payload: any): Promise<{ success: boolean; error?: string }> {
    const limiter = this.getRateLimiter(webhook.id, this.DEFAULT_RATE_LIMIT);

    try {
      await limiter.removeTokens(1);
      const controller = new AbortController();
      const timeout = setTimeout(() => controller.abort(), webhook.timeout || this.DEFAULT_TIMEOUT);

      const response = await fetch(webhook.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      clearTimeout(timeout);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  private getRateLimiter(webhookId: string, rateLimit: number): RateLimiter {
    if (!this.rateLimiters.has(webhookId)) {
      this.rateLimiters.set(webhookId, new RateLimiter({
        tokensPerInterval: rateLimit,
        interval: 'minute',
      }));
    }
    return this.rateLimiters.get(webhookId)!;
  }
}

export {
  WebhookService,
  WebhookCreateInput,
  WebhookUpdateInput,
  WebhookRetryConfig,
  Webhook,
  WebhookEvent,
  WebhookStats,
  WebhookQueueItem,
  WebhookHealth,
}; 