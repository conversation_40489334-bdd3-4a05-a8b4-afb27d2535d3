import { PrismaClient, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Run, PipelineRunStatus, User, Project } from '@prisma/client';
import { logger } from '../../utils/logger';
import { WebhookService } from '../webhook';
import { ActivityService } from '../activity';
import { PipelineEngine } from '../pipeline-engine';
import * as cron from 'node-cron';
import * as crypto from 'crypto';

// Define ActivityType locally (should match schema)
type ActivityType =
  | 'PIPELINE_CREATED'
  | 'PIPELINE_UPDATED'
  | 'PIPELINE_DELETED'
  | 'JOB_STARTED'
  | 'JOB_COMPLETED'
  | 'JOB_FAILED'
  | 'SECRET_CREATED'
  | 'SECRET_UPDATED'
  | 'SECRET_DELETED';

interface TriggerConfig {
  type: 'webhook' | 'schedule' | 'manual';
  config: {
    webhook?: {
      path: string;
      method: string;
      headers?: Record<string, string>;
      body?: any;
      secret?: string;
    };
    schedule?: {
      cron: string;
      timezone?: string;
      retryCount?: number;
      retryDelay?: number;
    };
  };
}

interface ScheduleJob {
  task: cron.ScheduledTask;
  pipelineId: string;
  cron: string;
  timezone?: string;
}

type PipelineWithRelations = Pipeline & {
  owner: User;
  project: Project;
};

type PipelineRunWithRelations = PipelineRun & {
  pipeline: PipelineWithRelations;
};

class PipelineTrigger {
  private prisma: PrismaClient;
  private webhookService: WebhookService;
  private activityService: ActivityService;
  private pipelineEngine: PipelineEngine;
  private scheduleJobs: Map<string, ScheduleJob>;
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 5000; // 5 seconds

  constructor(
    prisma: PrismaClient,
    webhookService: WebhookService,
    activityService: ActivityService,
    pipelineEngine: PipelineEngine
  ) {
    this.prisma = prisma;
    this.webhookService = webhookService;
    this.activityService = activityService;
    this.pipelineEngine = pipelineEngine;
    this.scheduleJobs = new Map();
  }

  private async getPipelineWithOwner(pipelineId: string): Promise<PipelineWithRelations> {
    const pipeline = await this.prisma.pipeline.findUnique({
      where: { id: pipelineId },
      // owner and project fields don't exist in schema - simplified
    });

    if (!pipeline) {
      throw new Error('Pipeline not found');
    }

    return pipeline as PipelineWithRelations;
  }

  public async registerTrigger(
    pipelineId: string,
    config: TriggerConfig
  ): Promise<void> {
    const pipeline = await this.getPipelineWithOwner(pipelineId);

    switch (config.type) {
      case 'webhook':
        if (!config.config.webhook) {
          throw new Error('Webhook configuration is required for webhook triggers');
        }
        await this.registerWebhookTrigger(pipeline, config.config.webhook);
        break;
      case 'schedule':
        if (!config.config.schedule) {
          throw new Error('Schedule configuration is required for schedule triggers');
        }
        await this.registerScheduleTrigger(pipeline, config.config.schedule);
        break;
      case 'manual':
        // Manual triggers don't need registration
        break;
      default:
        throw new Error(`Unsupported trigger type: ${config.type}`);
    }

    await this.activityService.logActivity({
      type: 'PIPELINE_UPDATED' as ActivityType,
      userId: 'system', // owner field doesn't exist
      resourceId: pipeline.id,
      details: {
        action: 'trigger_registered',
        triggerType: config.type,
      },
    });
  }

  private async registerWebhookTrigger(
    pipeline: PipelineWithRelations,
    config: NonNullable<TriggerConfig['config']['webhook']>
  ): Promise<void> {
    const webhook = await this.webhookService.createWebhook({
      name: `Pipeline ${pipeline.name} Webhook`,
      path: config.path,
      method: config.method,
      headers: config.headers,
      body: config.body,
      pipelineId: pipeline.id,
      secret: config.secret,
    });

    logger.info(`Registered webhook trigger for pipeline ${pipeline.name}`, {
      webhookId: webhook.id,
      path: config.path,
    });
  }

  private async registerScheduleTrigger(
    pipeline: PipelineWithRelations,
    config: NonNullable<TriggerConfig['config']['schedule']>
  ): Promise<void> {
    // Validate cron expression
    if (!cron.validate(config.cron)) {
      throw new Error(`Invalid cron expression: ${config.cron}`);
    }

    // Create scheduled task
    const task = cron.schedule(config.cron, async () => {
      try {
        await this.handleScheduleTrigger(pipeline.id);
      } catch (error: any) {
        logger.error(`Schedule trigger failed for pipeline ${pipeline.name}:`, error);
        
        // Retry logic
        const retryCount = config.retryCount || this.MAX_RETRIES;
        const retryDelay = config.retryDelay || this.RETRY_DELAY;
        
        for (let i = 0; i < retryCount; i++) {
          try {
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            await this.handleScheduleTrigger(pipeline.id);
            return;
          } catch (retryError: any) {
            logger.error(`Retry ${i + 1} failed for pipeline ${pipeline.name}:`, retryError);
          }
        }
      }
    }, {
      timezone: config.timezone,
    });

    // Store the scheduled task
    this.scheduleJobs.set(pipeline.id, {
      task,
      pipelineId: pipeline.id,
      cron: config.cron,
      timezone: config.timezone,
    });

    logger.info(`Registered schedule trigger for pipeline ${pipeline.name}`, {
      cron: config.cron,
      timezone: config.timezone,
    });
  }

  public async handleWebhookTrigger(
    webhookId: string,
    payload: any,
    signature?: string
  ): Promise<void> {
    const webhook = await this.webhookService.getWebhookById(webhookId);
    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Verify webhook signature if secret is configured
    if (webhook.secret && signature) {
      const hmac = crypto.createHmac('sha256', webhook.secret);
      const calculatedSignature = hmac.update(JSON.stringify(payload)).digest('hex');
      if (calculatedSignature !== signature) {
        throw new Error('Invalid webhook signature');
      }
    }

    const pipeline = await this.getPipelineWithOwner(webhook.pipelineId);
    await this.executePipeline(pipeline, {
      type: 'webhook',
      payload,
    });
  }

  public async handleScheduleTrigger(
    pipelineId: string
  ): Promise<void> {
    const pipeline = await this.getPipelineWithOwner(pipelineId);
    await this.executePipeline(pipeline, {
      type: 'schedule',
    });
  }

  public async executePipeline(
    pipeline: PipelineWithRelations,
    trigger: {
      type: 'webhook' | 'schedule' | 'manual';
      payload?: any;
    }
  ): Promise<void> {
    // Get the next run number
    const lastRun = await this.prisma.pipelineRun.findFirst({
      where: { pipelineId: pipeline.id },
      orderBy: { number: 'desc' },
    });

    const nextNumber = (lastRun?.number || 0) + 1;

    // Create pipeline run
    const run = await this.prisma.pipelineRun.create({
      data: {
        number: nextNumber,
        pipeline: { connect: { id: pipeline.id } },
        status: PipelineRunStatus.PENDING,
        // trigger field doesn't exist in schema - skipping
        startedAt: new Date(),
      },
    });

    try {
      // Execute pipeline
      const result = await this.pipelineEngine.executePipeline(
        pipeline.id,
        'system', // owner field doesn't exist
        'default' // project field doesn't exist
      );

      if (!result.success) {
        throw new Error(result.error || 'Pipeline execution failed');
      }

      // Update run status
      await this.prisma.pipelineRun.update({
        where: { id: run.id },
        data: {
          status: PipelineRunStatus.COMPLETED,
          finishedAt: new Date(),
        },
      });

      // Log activity
      await this.activityService.logActivity({
        type: 'JOB_COMPLETED' as ActivityType,
        userId: 'system', // owner field doesn't exist
        resourceId: pipeline.id,
        details: {
          action: 'pipeline_completed',
          runId: run.id,
        },
      });
    } catch (error: any) {
      // Update run status
      await this.prisma.pipelineRun.update({
        where: { id: run.id },
        data: {
          status: PipelineRunStatus.FAILED,
          finishedAt: new Date(),
          // variables field doesn't exist in schema - skipping
        },
      });

      // Log activity
      await this.activityService.logActivity({
        type: 'JOB_FAILED' as ActivityType,
        userId: 'system', // owner field doesn't exist
        resourceId: pipeline.id,
        details: {
          action: 'pipeline_failed',
          runId: run.id,
          error: error.message || 'Unknown error occurred',
        },
      });

      throw error;
    }
  }

  public async unregisterTrigger(
    pipelineId: string,
    type: TriggerConfig['type']
  ): Promise<void> {
    const pipeline = await this.getPipelineWithOwner(pipelineId);

    switch (type) {
      case 'webhook':
        await this.unregisterWebhookTrigger(pipeline);
        break;
      case 'schedule':
        await this.unregisterScheduleTrigger(pipeline);
        break;
      case 'manual':
        // Manual triggers don't need unregistration
        break;
      default:
        throw new Error(`Unsupported trigger type: ${type}`);
    }

    await this.activityService.logActivity({
      type: 'PIPELINE_UPDATED' as ActivityType,
      userId: 'system', // owner field doesn't exist
      resourceId: pipeline.id,
      details: {
        action: 'trigger_unregistered',
        triggerType: type,
      },
    });
  }

  private async unregisterWebhookTrigger(pipeline: PipelineWithRelations): Promise<void> {
    const webhook = await this.webhookService.getWebhookByPipelineId(pipeline.id);
    if (webhook) {
      await this.webhookService.deleteWebhook(webhook.id);
      logger.info(`Unregistered webhook trigger for pipeline ${pipeline.name}`);
    }
  }

  private async unregisterScheduleTrigger(pipeline: PipelineWithRelations): Promise<void> {
    const job = this.scheduleJobs.get(pipeline.id);
    if (job) {
      job.task.stop();
      this.scheduleJobs.delete(pipeline.id);
      logger.info(`Unregistered schedule trigger for pipeline ${pipeline.name}`);
    }
  }

  // Pipeline Run Management Methods
  public async getPipelineRuns(
    pipelineId: string,
    options: {
      limit?: number;
      offset?: number;
      status?: PipelineRunStatus;
    } = {}
  ): Promise<{ runs: PipelineRun[]; total: number }> {
    const { limit = 10, offset = 0, status } = options;

    const where = {
      pipelineId,
      ...(status && { status }),
    };

    const [runs, total] = await Promise.all([
      this.prisma.pipelineRun.findMany({
        where,
        orderBy: { number: 'desc' },
        take: limit,
        skip: offset,
      }),
      this.prisma.pipelineRun.count({ where }),
    ]);

    return { runs, total };
  }

  public async cancelPipelineRun(runId: string): Promise<void> {
    const run = await this.prisma.pipelineRun.findUnique({
      where: { id: runId },
      include: {
        pipeline: true,
      },
    }) as PipelineRunWithRelations | null;

    if (!run || !run.pipeline) {
      throw new Error('Pipeline run or pipeline not found');
    }

    if (run.status !== PipelineRunStatus.PENDING) {
      throw new Error('Only pending pipeline runs can be cancelled');
    }

    await this.prisma.pipelineRun.update({
      where: { id: runId },
      data: {
        status: PipelineRunStatus.CANCELLED,
        finishedAt: new Date(),
      },
    });

    // Log activity
    await this.activityService.logActivity({
      type: 'PIPELINE_UPDATED' as ActivityType,
      userId: 'system', // owner field doesn't exist
      resourceId: run.pipelineId,
      details: {
        action: 'pipeline_cancelled',
        runId: run.id,
      },
    });
  }

  public async cleanupOldPipelineRuns(
    options: {
      olderThan?: Date;
      maxRuns?: number;
    } = {}
  ): Promise<void> {
    const { olderThan = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), maxRuns = 100 } = options;

    // Get all pipelines
    const pipelines = await this.prisma.pipeline.findMany({
      select: { id: true },
    });

    for (const pipeline of pipelines) {
      // Get runs to delete
      const runsToDelete = await this.prisma.pipelineRun.findMany({
        where: {
          pipelineId: pipeline.id,
          createdAt: { lt: olderThan },
        },
        orderBy: { number: 'desc' },
        skip: maxRuns,
      });

      // Delete runs
      for (const run of runsToDelete) {
        await this.prisma.pipelineRun.delete({
          where: { id: run.id },
        });
      }

      if (runsToDelete.length > 0) {
        logger.info(`Cleaned up ${runsToDelete.length} old runs for pipeline ${pipeline.id}`);
      }
    }
  }
}

export { PipelineTrigger, TriggerConfig, PipelineWithRelations }; 