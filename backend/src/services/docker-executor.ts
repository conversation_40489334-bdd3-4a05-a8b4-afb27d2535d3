import Docker from 'dockerode';
import { logger } from '../utils/logger';
import { ActivityService } from './activity';

interface DockerConfig {
  image: string;
  command?: string[];
  env?: Record<string, string>;
  volumes?: string[];
  ports?: Record<string, string>;
  network?: string;
  workingDir?: string;
  user?: string;
  memory?: string;
  cpu?: string;
}

class DockerExecutor {
  private static instance: DockerExecutor;
  private docker: Docker;
  private activityService: ActivityService;

  private constructor() {
    this.docker = new Docker();
    this.activityService = ActivityService.getInstance();
  }

  public static getInstance(): DockerExecutor {
    if (!DockerExecutor.instance) {
      DockerExecutor.instance = new DockerExecutor();
    }
    return DockerExecutor.instance;
  }

  public async executeStep(
    config: DockerConfig,
    userId: string,
    projectId: string,
    pipelineId: string
  ): Promise<{ success: boolean; output: string; error?: string }> {
    try {
      // Pull the image if it doesn't exist
      await this.pullImage(config.image);

      // Create container
      const container = await this.docker.createContainer({
        Image: config.image,
        Cmd: config.command,
        Env: Object.entries(config.env || {}).map(([key, value]) => `${key}=${value}`),
        HostConfig: {
          Binds: config.volumes,
          PortBindings: this.mapPorts(config.ports),
          NetworkMode: config.network,
          // WorkingDir doesn't exist in HostConfig - moved outside
          // User doesn't exist in HostConfig - moved outside
          Memory: this.parseMemory(config.memory),
          NanoCpus: this.parseCPU(config.cpu),
        },
        WorkingDir: config.workingDir,
        User: config.user,
      });

      // Start container
      await (container as any).start();

      // Wait for container to finish
      const result = await (container as any).wait();

      // Get container logs
      const logs = await (container as any).logs({
        follow: false,
        stdout: true,
        stderr: true,
      });

      // Remove container
      await (container as any).remove();

      // Log activity
      await this.activityService.logActivity({
        type: 'JOB_COMPLETED',
        userId,
        projectId,
        resourceId: pipelineId,
        details: {
          image: config.image,
          exitCode: result.StatusCode,
        },
      });

      return {
        success: result.StatusCode === 0,
        output: logs.toString(),
        error: result.StatusCode !== 0 ? `Container exited with code ${result.StatusCode}` : undefined,
      };
    } catch (error) {
      logger.error('Docker execution failed:', error);

      // Log activity
      await this.activityService.logActivity({
        type: 'JOB_FAILED',
        userId,
        projectId,
        resourceId: pipelineId,
        details: {
          image: config.image,
          error: (error as Error).message,
        },
      });

      return {
        success: false,
        output: '',
        error: (error as Error).message,
      };
    }
  }

  private async pullImage(image: string): Promise<void> {
    try {
      await this.docker.pull(image);
    } catch (error) {
      logger.error(`Failed to pull image ${image}:`, error);
      throw error;
    }
  }

  private mapPorts(ports?: Record<string, string>): Record<string, any> {
    if (!ports) return {};

    return Object.entries(ports).reduce((acc, [containerPort, hostPort]) => {
      acc[`${containerPort}/tcp`] = [{ HostPort: hostPort }];
      return acc;
    }, {} as Record<string, any>);
  }

  private parseMemory(memory?: string): number {
    if (!memory) return 0;

    const units: Record<string, number> = {
      b: 1,
      k: 1024,
      m: 1024 * 1024,
      g: 1024 * 1024 * 1024,
    };

    const match = memory.match(/^(\d+)([bkmg])?$/i);
    if (!match) return 0;

    const [, value, unit = 'b'] = match;
    return parseInt(value) * (units[unit.toLowerCase()] || 1);
  }

  private parseCPU(cpu?: string): number {
    if (!cpu) return 0;

    const match = cpu.match(/^(\d+)([m])?$/i);
    if (!match) return 0;

    const [, value, unit = ''] = match;
    return unit.toLowerCase() === 'm' ? parseInt(value) * 1000000 : parseInt(value) * 1000000000;
  }
}

export { DockerExecutor, DockerConfig }; 