import { Router, Request, Response } from 'express';
import { AuditLogger } from '../services/auditLogger';
import { AuditAction, AuditResource } from '../types/audit';
import { authenticate, AuthenticatedRequest, asyncHandler } from '../middleware/auth';
import { auditMiddleware } from '../middleware/auditMiddleware';
import { logger } from '../lib/logger';

const router = Router();
const auditLogger = AuditLogger.getInstance();

/**
 * GET /api/audit/logs
 * Get audit logs with filtering and pagination
 */
router.get('/logs',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const authReq = req as AuthenticatedRequest;
    try {
      const {
        userId,
        action,
        resource,
        resourceId,
        success,
        startDate,
        endDate,
        limit = '50',
        offset = '0',
      } = authReq.query;

      // Check if user has permission to view audit logs
      if (authReq.user?.role !== 'ADMIN') {
        return res.status(403).json({
          error: 'Insufficient permissions to view audit logs',
        });
      }

      const options = {
        userId: userId as string,
        action: action as AuditAction,
        resource: resource as AuditResource,
        resourceId: resourceId as string,
        success: success ? success === 'true' : undefined,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        limit: parseInt(limit as string, 10),
        offset: parseInt(offset as string, 10),
      };

      const result = await auditLogger.getAuditLogs(options);

      return res.json({
        logs: result.logs,
        total: result.total,
        limit: options.limit,
        offset: options.offset,
      });
    } catch (error) {
      logger.error('Error fetching audit logs:', error);
      return res.status(500).json({
        error: 'Failed to fetch audit logs',
      });
    }
  })
);

/**
 * GET /api/audit/stats
 * Get audit statistics
 */
router.get('/stats',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const authReq = req as AuthenticatedRequest;
      const { userId, startDate, endDate } = authReq.query;

      // Check if user has permission to view audit stats
      if (authReq.user?.role !== 'ADMIN') {
        return res.status(403).json({
          error: 'Insufficient permissions to view audit statistics',
        });
      }

      const options = {
        userId: userId as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
      };

      const stats = await auditLogger.getAuditStats(options);

      return res.json(stats);
    } catch (error) {
      logger.error('Error fetching audit stats:', error);
      return res.status(500).json({
        error: 'Failed to fetch audit statistics',
      });
    }
  })
);

/**
 * GET /api/audit/actions
 * Get available audit actions
 */
router.get('/actions',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const actions = Object.values(AuditAction);
      return res.json({ actions });
    } catch (error) {
      logger.error('Error fetching audit actions:', error);
      return res.status(500).json({
        error: 'Failed to fetch audit actions',
      });
    }
  })
);

/**
 * GET /api/audit/resources
 * Get available audit resources
 */
router.get('/resources',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const resources = Object.values(AuditResource);
      return res.json({ resources });
    } catch (error) {
      logger.error('Error fetching audit resources:', error);
      return res.status(500).json({
        error: 'Failed to fetch audit resources',
      });
    }
  })
);

/**
 * GET /api/audit/logs/:id
 * Get a specific audit log entry
 */
router.get('/logs/:id',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const authReq = req as AuthenticatedRequest;
      const { id } = authReq.params;

      // Check if user has permission to view audit logs
      if (authReq.user?.role !== 'ADMIN') {
        return res.status(403).json({
          error: 'Insufficient permissions to view audit logs',
        });
      }

      const result = await auditLogger.getAuditLogs({
        limit: 1,
        offset: 0,
      });

      // Find the specific log by ID
      const log = result.logs.find((l: any) => l.id === id);
      
      if (!log) {
        return res.status(404).json({
          error: 'Audit log not found',
        });
      }

      return res.json(log);
    } catch (error) {
      logger.error('Error fetching audit log:', error);
      return res.status(500).json({
        error: 'Failed to fetch audit log',
      });
    }
  })
);

/**
 * POST /api/audit/export
 * Export audit logs to CSV
 */
router.post('/export',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const authReq = req as AuthenticatedRequest;
      const {
        userId,
        action,
        resource,
        resourceId,
        success,
        startDate,
        endDate,
      } = authReq.body;

      // Check if user has permission to export audit logs
      if (authReq.user?.role !== 'ADMIN') {
        return res.status(403).json({
          error: 'Insufficient permissions to export audit logs',
        });
      }

      const options = {
        userId,
        action,
        resource,
        resourceId,
        success,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        limit: 10000, // Large limit for export
        offset: 0,
      };

      const result = await auditLogger.getAuditLogs(options);

      // Convert to CSV format
      const csvHeaders = [
        'ID',
        'Action',
        'Resource',
        'Resource ID',
        'User ID',
        'Username',
        'IP Address',
        'Success',
        'Error',
        'Duration (ms)',
        'Created At',
      ];

      const csvRows = result.logs.map((log: any) => [
        log.id,
        log.action,
        log.resource,
        log.resourceId || '',
        log.userId || '',
        log.user?.username || '',
        log.ipAddress || '',
        log.success,
        log.error || '',
        log.duration || '',
        log.createdAt.toISOString(),
      ]);

      const csvContent = [
        csvHeaders.join(','),
        ...csvRows.map((row: any[]) => row.map((field: any) => `"${field}"`).join(',')),
      ].join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=audit-logs.csv');
      return res.send(csvContent);
    } catch (error) {
      logger.error('Error exporting audit logs:', error);
      return res.status(500).json({
        error: 'Failed to export audit logs',
      });
    }
  })
);

/**
 * GET /api/audit/user/:userId/logs
 * Get audit logs for a specific user
 */
router.get('/user/:userId/logs',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const authReq = req as AuthenticatedRequest;
      const { userId } = authReq.params;
      const { limit = '50', offset = '0' } = authReq.query;

      // Check if user has permission or is viewing their own logs
      if (authReq.user?.role !== 'ADMIN' && authReq.user?.id !== userId) {
        return res.status(403).json({
          error: 'Insufficient permissions to view user audit logs',
        });
      }

      const options = {
        userId,
        limit: parseInt(limit as string, 10),
        offset: parseInt(offset as string, 10),
      };

      const result = await auditLogger.getAuditLogs(options);

      return res.json({
        logs: result.logs,
        total: result.total,
        limit: options.limit,
        offset: options.offset,
      });
    } catch (error) {
      logger.error('Error fetching user audit logs:', error);
      return res.status(500).json({
        error: 'Failed to fetch user audit logs',
      });
    }
  })
);

export default router;
