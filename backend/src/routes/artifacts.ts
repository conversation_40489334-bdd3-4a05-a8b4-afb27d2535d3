import { Router, Request, Response } from 'express';
import { ArtifactService } from '../services/artifacts';
import { asyncHandler } from '../middleware/asyncHandler';
import { authenticate } from '../middleware/auth';
import multer from 'multer';
import { join } from 'path';
import { mkdir } from 'fs/promises';

const router = Router();
const upload = multer({ dest: 'uploads/' });

// Initialize artifact service with config from environment variables
const artifactService = ArtifactService.getInstance({
  bucket: process.env.AWS_S3_BUCKET || '',
  region: process.env.AWS_REGION || '',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
});

/**
 * @route POST /api/artifacts/upload
 * @desc Upload an artifact for a job
 */
router.post(
  '/upload',
  authenticate,
  upload.single('artifact') as any,
  asyncHandler(async (req: any, res: Response) => {
    const { jobId } = req.body;
    const file = req.file;

    if (!jobId || !file) {
      return res.status(400).json({
        error: 'Job ID and artifact file are required'
      });
    }

    const key = await artifactService.uploadArtifact(
      jobId,
      file.path,
      file.originalname
    );

    return res.json({ key });
  })
);

/**
 * @route GET /api/artifacts/download/:jobId/:artifactName
 * @desc Download an artifact
 */
router.get(
  '/download/:jobId/:artifactName',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { jobId, artifactName } = req.params;
    const destinationPath = join('downloads', jobId);

    await artifactService.downloadArtifact(
      jobId,
      artifactName,
      destinationPath
    );

    const filePath = join(destinationPath, artifactName);
    return res.download(filePath);
  })
);

/**
 * @route GET /api/artifacts/list/:jobId
 * @desc List artifacts for a job
 */
router.get(
  '/list/:jobId',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { jobId } = req.params;
    const artifacts = await artifactService.listArtifacts(jobId);
    return res.json({ artifacts });
  })
);

/**
 * @route DELETE /api/artifacts/:jobId/:artifactName
 * @desc Delete an artifact
 */
router.delete(
  '/:jobId/:artifactName',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { jobId, artifactName } = req.params;
    await artifactService.deleteArtifact(jobId, artifactName);
    return res.json({ message: 'Artifact deleted successfully' });
  })
);

/**
 * @route GET /api/artifacts/metadata/:jobId/:artifactName
 * @desc Get artifact metadata
 */
router.get(
  '/metadata/:jobId/:artifactName',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { jobId, artifactName } = req.params;
    const metadata = await artifactService.getArtifactMetadata(jobId, artifactName);
    return res.json(metadata);
  })
);

export default router; 