import { Router } from 'express';
import { getAuditLogs, getAuditLogById, getAuditStats } from '../controllers/auditLogController';
import { authenticate } from '../middleware/auth';
import { authorize } from '../middleware/rbac';
// UserRole doesn't exist in Prisma schema, using string literals

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get all audit logs with pagination and filters
router.get('/', authorize(['ADMIN']), getAuditLogs);

// Get audit log by ID
router.get('/:id', authorize(['ADMIN']), getAuditLogById);

// Get audit statistics
router.get('/stats', authorize(['ADMIN']), getAuditStats);

export default router; 